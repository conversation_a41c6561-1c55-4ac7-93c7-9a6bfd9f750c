'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { debounce } from '@/lib/utils/format'

interface UseDebouncedTimeframeProps {
  initialStart: number
  initialEnd: number
  onUpdate: (start: number, end: number) => void
  debounceMs?: number
}

export function useDebouncedTimeframe({
  initialStart,
  initialEnd,
  onUpdate,
  debounceMs = 100
}: UseDebouncedTimeframeProps) {
  const [localStart, setLocalStart] = useState(initialStart)
  const [localEnd, setLocalEnd] = useState(initialEnd)
  const [isUpdating, setIsUpdating] = useState(false)
  
  // Update local state when props change
  useEffect(() => {
    setLocalStart(initialStart)
    setLocalEnd(initialEnd)
  }, [initialStart, initialEnd])

  // Create debounced update function
  const debouncedUpdate = useCallback(
    debounce((start: number, end: number) => {
      onUpdate(start, end)
      setIsUpdating(false)
    }, debounceMs),
    [onUpdate, debounceMs]
  )

  // Update function that provides immediate visual feedback
  const updateTimeframe = useCallback((newStart: number, newEnd: number) => {
    setLocalStart(newStart)
    setLocalEnd(newEnd)
    setIsUpdating(true)
    debouncedUpdate(newStart, newEnd)
  }, [debouncedUpdate])

  return {
    localStart,
    localEnd,
    isUpdating,
    updateTimeframe
  }
}
