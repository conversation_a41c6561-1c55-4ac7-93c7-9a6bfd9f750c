'use client'

import { useState, useEffect } from 'react'
import { useNavigation } from '@/hooks/useNavigation'
import { useDraftQueue } from '@/hooks/useTublistWorkshop'
import { useI18n } from '@/hooks/useI18n'
import { useDebouncedTimeframe } from '@/hooks/useDebouncedTimeframe'
import { FocusedRangeSlider } from '@/components/ui/FocusedRangeSlider'
import { InlineTimeframePreview } from '@/components/tublist-workshop/InlineTimeframePreview'
import { formatTime } from '@/lib/utils/time'
import { DraftVideoItem, VideoTimeframe } from '@/lib/types/video'

export function VideoLoopEditor() {
  const { timeframeEditingState, exitTimeframeEditing } = useNavigation()
  const { draftItems, addTimeframe, updateTimeframe, updateDraftItem, removeTimeframe } = useDraftQueue()

  const [loopCount, setLoopCount] = useState(1)
  const { t } = useI18n()

  // Video-wide loop settings state
  const [videoLoopCount, setVideoLoopCount] = useState(1)
  const [loopMode, setLoopMode] = useState<'timeframes-only' | 'whole-video-plus-timeframes'>('timeframes-only')

  // Timeframe management state
  const [editingTimeframeId, setEditingTimeframeId] = useState<string | null>(null)
  const [isCreatingTimeframe, setIsCreatingTimeframe] = useState(false)

  // Find the item being edited
  const item = timeframeEditingState ? draftItems.find(item => item.draftId === timeframeEditingState.itemId) : null

  // Determine which timeframe we're working with
  const editingTimeframe = editingTimeframeId && item
    ? item.timeframes.find(tf => tf.id === editingTimeframeId)
    : undefined

  // Initialize values for timeframe creation/editing
  const initialStart = editingTimeframe?.startTime || 0
  const initialEnd = editingTimeframe?.endTime || Math.min(30, item?.duration || 30)

  useEffect(() => {
    if (editingTimeframe) {
      setLoopCount(editingTimeframe.loopCount)
    }
    if (item) {
      setVideoLoopCount(item.loopSettings.videoLoopCount)
      setLoopMode(item.loopSettings.loopMode)
    }
  }, [editingTimeframe, item])

  // Auto-start creating timeframe if coming from "create" mode
  useEffect(() => {
    if (timeframeEditingState?.mode === 'create') {
      setIsCreatingTimeframe(true)
    } else if (timeframeEditingState?.timeframeId) {
      setEditingTimeframeId(timeframeEditingState.timeframeId)
    }
  }, [timeframeEditingState])

  // Ensure the component starts at the top when it opens
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'instant' })
  }, [])

  const { localStart, localEnd, isUpdating, updateTimeframe: updateLocalTimeframe } = useDebouncedTimeframe({
    initialStart,
    initialEnd,
    onUpdate: editingTimeframe && item
      ? (start, end) => updateTimeframe(item.draftId, editingTimeframe.id, { startTime: start, endTime: end })
      : () => {},
    debounceMs: 50
  })

  // Validation
  const isValidTimeframe = item && localEnd > localStart && localStart >= 0 && localEnd <= item.duration
  const showTimeframeEditor = isCreatingTimeframe || editingTimeframeId

  // Handle save video settings
  const handleSaveVideoSettings = () => {
    if (!item) return

    updateDraftItem(item.draftId, {
      loopSettings: {
        videoLoopCount,
        loopMode
      }
    })

    exitTimeframeEditing()
  }

  // Handle save timeframe
  const handleSaveTimeframe = () => {
    if (!item || !isValidTimeframe) return

    if (isCreatingTimeframe) {
      addTimeframe(item.draftId, localStart, localEnd, loopCount)
      setIsCreatingTimeframe(false)
    } else if (editingTimeframe) {
      updateTimeframe(item.draftId, editingTimeframe.id, { loopCount })
      setEditingTimeframeId(null)
    }
  }

  // Handle delete timeframe
  const handleDeleteTimeframe = (timeframeId: string) => {
    if (!item) return
    removeTimeframe(item.draftId, timeframeId)
    if (editingTimeframeId === timeframeId) {
      setEditingTimeframeId(null)
    }
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return

      if (e.key === 'Escape') {
        if (showTimeframeEditor) {
          setIsCreatingTimeframe(false)
          setEditingTimeframeId(null)
        } else {
          exitTimeframeEditing()
        }
      } else if (e.key === 'Enter' && showTimeframeEditor && isValidTimeframe) {
        handleSaveTimeframe()
      } else if (e.key === 'ArrowLeft' && item) {
        e.preventDefault()
        updateLocalTimeframe(Math.max(0, localStart - 1), localEnd)
      } else if (e.key === 'ArrowRight' && item) {
        e.preventDefault()
        updateLocalTimeframe(Math.min(item.duration - 1, localStart + 1), localEnd)
      } else if (e.key === 'ArrowUp' && item) {
        e.preventDefault()
        updateLocalTimeframe(localStart, Math.min(item.duration, localEnd + 1))
      } else if (e.key === 'ArrowDown' && item) {
        e.preventDefault()
        updateLocalTimeframe(localStart, Math.max(localStart + 1, localEnd - 1))
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [exitTimeframeEditing, isValidTimeframe, localStart, localEnd, updateLocalTimeframe, item, handleSaveTimeframe, showTimeframeEditor])

  // If no editing state or item, redirect back
  if (!timeframeEditingState || !item) {
    exitTimeframeEditing()
    return null
  }

  return (
    <div className="min-h-screen bg-dark-900 flex flex-col">
      {/* Navigation Header */}
      <header className="bg-dark-800 border-b border-dark-700 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={exitTimeframeEditing}
              className="flex items-center gap-2 text-dark-300 hover:text-white transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
              </svg>
              Back to Queue
            </button>
            <div className="h-6 w-px bg-dark-600"></div>
            <div>
              <h1 className="text-xl font-bold text-white">
                {t('timeframeEditor.videoLoopEditor')}
              </h1>
              <p className="text-sm text-dark-400">
                {t('timeframeEditor.configureSettings')}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {isUpdating && (
              <div className="flex items-center gap-2 text-primary-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-400"></div>
                <span className="text-sm">{t('status.savingChanges')}</span>
              </div>
            )}
            
            <div className="flex items-center gap-3">
              <button
                onClick={exitTimeframeEditing}
                className="flex items-center justify-center w-10 h-10 bg-dark-600 hover:bg-dark-500 text-white rounded-lg transition-all hover:scale-105 active:scale-95"
                title={t('common.cancel')}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
              <button
                onClick={handleSaveVideoSettings}
                className="flex items-center justify-center w-10 h-10 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-all hover:scale-105 active:scale-95"
                title={t('buttons.saveAndExit')}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto">
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Video Information */}
          <div className="mb-8">
            <div className="flex items-center gap-6 p-6 bg-dark-800 rounded-2xl border border-dark-700">
              <img 
                src={item.thumbnail} 
                alt={item.title}
                className="w-32 h-24 object-cover rounded-xl shadow-lg"
              />
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-white mb-2 line-clamp-2">{item.title}</h2>
                <div className="flex items-center gap-6 text-dark-300">
                  <span className="flex items-center gap-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    Duration: {formatTime(item.duration)}
                  </span>
                  {item.channel && (
                    <>
                      <span>•</span>
                      <span>{item.channel}</span>
                    </>
                  )}
                  <span>•</span>
                  <span>{item.timeframes.length} existing timeframes</span>
                </div>
              </div>
            </div>
          </div>

          {/* General Video Loop Settings Section */}
          <div className="mb-12">
            <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-2xl p-8 border border-purple-500/30">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-white">{t('timeframeEditor.generalVideoSettings')}</h3>
                <span className="text-sm text-purple-300 bg-purple-500/20 px-3 py-1 rounded-full">
                  {t('timeframeEditor.appliesToEntireVideo')}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Video Loop Count */}
                <div>
                  <label className="text-lg font-semibold text-white block mb-4">
                    {t('timeframeEditor.videoLoopCount')}
                  </label>
                  <div className="flex items-center gap-4">
                    <input
                      type="number"
                      min="1"
                      max="99"
                      value={videoLoopCount}
                      onChange={(e) => setVideoLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
                      className="input-field text-xl py-3 px-4 w-24 text-center font-mono"
                    />
                    <div className="text-dark-300">
                      <div className="text-sm text-dark-400">{t('timeframeEditor.howManyTimesVideo')}</div>
                      <div className="text-sm text-purple-300">
                        {t('timeframeEditor.totalVideoDuration')}: {formatTime(item.duration * videoLoopCount)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Loop Mode */}
                <div>
                  <label className="text-lg font-semibold text-white block mb-4">
                    {t('timeframeEditor.loopMode')}
                  </label>
                  <select
                    value={loopMode}
                    onChange={(e) => setLoopMode(e.target.value as 'timeframes-only' | 'whole-video-plus-timeframes')}
                    className="input-field text-lg py-3 px-4 w-full"
                  >
                    <option value="timeframes-only">{t('timeframeEditor.timeframesOnly')}</option>
                    <option value="whole-video-plus-timeframes">{t('timeframeEditor.fullVideoPlusTimeframes')}</option>
                  </select>
                  <div className="text-sm text-dark-400 mt-2 p-3 bg-dark-700/30 rounded-lg">
                    {loopMode === 'timeframes-only'
                      ? t('timeframeEditor.playOnlyTimeframes')
                      : t('timeframeEditor.playFullThenTimeframes')
                    }
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section Separator */}
          <div className="flex items-center gap-4 mb-8">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-dark-500 to-transparent"></div>
            <div className="text-dark-400 text-sm font-medium px-4 py-2 bg-dark-700/50 rounded-full">
              {t('timeframeEditor.configureTimeframe')}
            </div>
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-dark-500 to-transparent"></div>
          </div>

          {/* Timeframe Management Section */}
          <div className="bg-gradient-to-r from-green-900/20 to-teal-900/20 rounded-2xl p-8 border border-green-500/30 mb-8">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Timeframe Management</h3>
                  <span className="text-sm text-green-300">
                    {item.timeframes.length} existing timeframe{item.timeframes.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>

              {/* Header Action Buttons */}
              <div className="flex items-center gap-3">
                {showTimeframeEditor ? (
                  <>
                    <button
                      onClick={() => {
                        setIsCreatingTimeframe(false)
                        setEditingTimeframeId(null)
                      }}
                      className="flex items-center justify-center w-10 h-10 bg-dark-600 hover:bg-dark-500 text-white rounded-lg transition-all hover:scale-105 active:scale-95"
                      title="Cancel (Esc)"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                      </svg>
                    </button>
                    <button
                      onClick={handleSaveTimeframe}
                      disabled={!isValidTimeframe}
                      className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all ${
                        isValidTimeframe
                          ? 'bg-green-600 hover:bg-green-700 text-white hover:scale-105 active:scale-95'
                          : 'bg-dark-600 text-dark-400 cursor-not-allowed'
                      }`}
                      title={isCreatingTimeframe ? 'Create Timeframe (Enter)' : 'Save Changes (Enter)'}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => setIsCreatingTimeframe(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-all"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    Add Timeframe
                  </button>
                )}
              </div>
            </div>

            {/* Existing Timeframes List */}
            {!showTimeframeEditor && item.timeframes.length > 0 && (
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-white mb-4">Existing Timeframes</h4>
                <div className="space-y-3">
                  {item.timeframes.map((tf, index) => (
                    <div key={tf.id} className="flex items-center justify-between p-4 bg-dark-800/50 rounded-xl border border-dark-700/50">
                      <div className="flex items-center gap-4">
                        <span className="bg-green-600/30 text-green-300 px-2 py-1 rounded-lg text-sm font-mono font-semibold">
                          #{index + 1}
                        </span>
                        <div>
                          <div className="text-white font-medium">
                            {formatTime(tf.startTime)} - {formatTime(tf.endTime)}
                          </div>
                          <div className="text-sm text-dark-400">
                            Duration: {formatTime(tf.endTime - tf.startTime)} • Loops: {tf.loopCount}x
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setEditingTimeframeId(tf.id)}
                          className="text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-blue-500/10 transition-all"
                          title="Edit timeframe"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeleteTimeframe(tf.id)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-500/10 transition-all"
                          title="Delete timeframe"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Timeframe Editor (Create/Edit) */}
            {showTimeframeEditor && (

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* Left Column - Main Timeframe Controls */}
              <div className="lg:col-span-2 space-y-4">
                {/* Range Slider Section - Compact */}
                <div className="bg-dark-800/50 rounded-xl p-6 border border-dark-700/50">
                  <h4 className="text-lg font-bold text-white mb-4">Time Range Selection</h4>

                  <FocusedRangeSlider
                    min={0}
                    max={item.duration}
                    startValue={localStart}
                    endValue={localEnd}
                    onChange={updateLocalTimeframe}
                    step={1}
                    className=""
                  />
                </div>

                {/* Preview Section - Always visible below slider */}
                <div className="bg-dark-800/50 rounded-xl p-4 border border-dark-700/50">
                  <h4 className="text-base font-bold text-white mb-3">Timeframe Preview</h4>

                  <div className="w-full">
                    <InlineTimeframePreview
                      videoId={item.id}
                      timeframe={{
                        id: editingTimeframe?.id || 'preview',
                        startTime: localStart,
                        endTime: localEnd,
                        loopCount
                      }}
                      onClose={() => {}}
                    />
                  </div>
                </div>
              </div>

              {/* Right Column - Timeframe-Specific Settings */}
              <div className="lg:col-span-1 space-y-4">
                {/* Individual Timeframe Loop Count */}
                <div className="bg-dark-800/50 rounded-xl p-4 border border-dark-700/50">
                  <h4 className="text-base font-bold text-white mb-3">Timeframe Loop Count</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-xs text-dark-300 block mb-2">
                        How many times this timeframe loops
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="99"
                        value={loopCount}
                        onChange={(e) => setLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
                        className="input-field text-lg py-2 px-3 w-full text-center font-mono"
                      />
                    </div>
                    <div className="text-center p-3 bg-dark-700/50 rounded-lg">
                      <div className="text-xs text-dark-400 mb-1">Timeframe Duration</div>
                      <div className="text-lg font-mono font-bold text-green-300">
                        {formatTime((localEnd - localStart) * loopCount)}
                      </div>
                    </div>
                  </div>
                </div>



                {/* Keyboard Shortcuts Help */}
                <div className="bg-dark-800/30 rounded-lg p-3 border border-dark-700/30">
                  <h5 className="text-xs font-semibold text-white mb-2">Keyboard Shortcuts</h5>
                  <div className="space-y-1 text-xs text-dark-400">
                    <div><kbd className="bg-dark-600 px-1.5 py-0.5 rounded text-xs">Esc</kbd> Cancel</div>
                    <div><kbd className="bg-dark-600 px-1.5 py-0.5 rounded text-xs">Enter</kbd> Save</div>
                    <div><kbd className="bg-dark-600 px-1.5 py-0.5 rounded text-xs">←→</kbd> Start</div>
                    <div><kbd className="bg-dark-600 px-1.5 py-0.5 rounded text-xs">↑↓</kbd> End</div>
                  </div>
                </div>
              </div>


            </div>
            )}
          </div>

          {/* Combined Settings Summary */}
          <div className="bg-dark-800/30 rounded-2xl p-6 border border-dark-600/50 mb-8">
            <h4 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-blue-400">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Playback Summary
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-purple-500/10 rounded-lg p-4 border border-purple-500/20">
                <div className="text-purple-300 font-semibold mb-1">Video Settings</div>
                <div className="text-dark-300">
                  • Loops {videoLoopCount} time{videoLoopCount > 1 ? 's' : ''}
                </div>
                <div className="text-dark-300">
                  • Mode: {loopMode === 'timeframes-only' ? 'Timeframes Only' : 'Full + Timeframes'}
                </div>
              </div>
              <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/20">
                <div className="text-green-300 font-semibold mb-1">Timeframes</div>
                <div className="text-dark-300">
                  • Total: {item.timeframes.length} timeframe{item.timeframes.length !== 1 ? 's' : ''}
                </div>
                <div className="text-dark-300">
                  • Combined duration: {formatTime(item.timeframes.reduce((total, tf) => total + (tf.endTime - tf.startTime), 0))}
                </div>
              </div>
              <div className="bg-blue-500/10 rounded-lg p-4 border border-blue-500/20">
                <div className="text-blue-300 font-semibold mb-1">Total Playback</div>
                <div className="text-dark-300">
                  • Video plays: {videoLoopCount} time{videoLoopCount > 1 ? 's' : ''}
                </div>
                <div className="text-dark-300">
                  • Est. duration: {formatTime(
                    loopMode === 'timeframes-only'
                      ? item.timeframes.reduce((total, tf) => total + (tf.endTime - tf.startTime) * tf.loopCount, 0) * videoLoopCount
                      : item.duration * videoLoopCount
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Validation Error */}
          {!isValidTimeframe && (
            <div className="text-center">
              <div className="inline-block text-red-400 bg-red-500/10 px-8 py-4 rounded-xl border border-red-500/20">
                <div className="font-semibold mb-1">Invalid Timeframe</div>
                <div className="text-sm">End time must be greater than start time and within video duration</div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
