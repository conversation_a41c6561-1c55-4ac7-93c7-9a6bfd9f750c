'use client'

import { useState } from 'react'
import { useDebouncedTimeframe } from '@/hooks/useDebouncedTimeframe'
import { RangeSlider } from '@/components/ui/RangeSlider'

interface NewTimeframeFormProps {
  maxDuration: number
  onAdd: (startTime: number, endTime: number, loopCount: number) => void
  onCancel: () => void
}

export function NewTimeframeForm({ maxDuration, onAdd, onCancel }: NewTimeframeFormProps) {
  const [loopCount, setLoopCount] = useState(1)
  const [localStartTime, setLocalStartTime] = useState(0)
  const [localEndTime, setLocalEndTime] = useState(Math.min(30, maxDuration))

  const isValidTimeframe = localEndTime > localStartTime && localStartTime >= 0 && localEndTime <= maxDuration

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isValidTimeframe) {
      handleAddTimeframe()
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  const handleAddTimeframe = () => {
    if (isValidTimeframe) {
      onAdd(localStartTime, localEndTime, loopCount)
    }
  }

  const handleTimeframeChange = (start: number, end: number) => {
    setLocalStartTime(start)
    setLocalEndTime(end)
  }

  return (
    <div className="bg-dark-600/60 rounded-xl p-5 space-y-5 border border-dark-500/50 shadow-lg">
      {/* Header with Action Buttons */}
      <div className="flex items-center justify-between mb-4">
        <label className="text-base text-dark-100 font-semibold">Select Time Range</label>
        <div className="flex items-center gap-3">
          <button
            onClick={onCancel}
            className="flex items-center justify-center w-10 h-10 bg-dark-600 hover:bg-dark-500 text-white rounded-lg transition-all hover:scale-105 active:scale-95"
            title="Cancel (Esc)"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
          <button
            onClick={handleAddTimeframe}
            disabled={!isValidTimeframe}
            className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all ${
              isValidTimeframe
                ? 'bg-green-600 hover:bg-green-700 text-white hover:scale-105 active:scale-95'
                : 'bg-dark-600 text-dark-400 cursor-not-allowed'
            }`}
            title="Add Timeframe (Enter)"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
          </button>
        </div>
      </div>

      <div>
        <RangeSlider
          min={0}
          max={maxDuration}
          startValue={localStartTime}
          endValue={localEndTime}
          onChange={handleTimeframeChange}
          step={1}
          className="mb-4"
        />
      </div>

      <div>
        <label className="text-sm text-dark-200 font-medium block mb-3">Loop Count</label>
        <input
          type="number"
          min="1"
          max="99"
          value={loopCount}
          onChange={(e) => setLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
          onKeyDown={handleKeyDown}
          className="input-field text-sm py-2 px-3 w-24"
        />
      </div>



      {!isValidTimeframe && (
        <div className="text-sm text-red-400 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
          End time must be greater than start time and within video duration
        </div>
      )}

      <div className="text-xs text-dark-400 text-center p-2 bg-dark-700/30 rounded-lg">
        Press <kbd className="bg-dark-600 px-1 rounded">Enter</kbd> to add, <kbd className="bg-dark-600 px-1 rounded">Escape</kbd> to cancel
      </div>
    </div>
  )
}
