'use client'

import { useState, useEffect } from 'react'
import { useDebouncedTimeframe } from '@/hooks/useDebouncedTimeframe'
import { FocusedRangeSlider } from '@/components/ui/FocusedRangeSlider'
import { InlineTimeframePreview } from './InlineTimeframePreview'
import { VideoTimeframe, DraftVideoItem } from '@/lib/types/video'
import { formatTime } from '@/lib/utils/time'

interface FocusedTimeframeEditorProps {
  item: DraftVideoItem
  timeframe?: VideoTimeframe // undefined for new timeframe
  onUpdate?: (timeframeId: string, updates: Partial<Pick<VideoTimeframe, 'startTime' | 'endTime' | 'loopCount'>>) => void
  onAdd?: (startTime: number, endTime: number, loopCount: number) => void
  onClose: () => void
  mode: 'edit' | 'create'
  allItems?: DraftVideoItem[] // for context display
}

export function FocusedTimeframeEditor({
  item,
  timeframe,
  onUpdate,
  onAdd,
  onClose,
  mode,
  allItems = []
}: FocusedTimeframeEditorProps) {
  const [loopCount, setLoopCount] = useState(timeframe?.loopCount || 1)
  
  const initialStart = timeframe?.startTime || 0
  const initialEnd = timeframe?.endTime || Math.min(30, item.duration)

  const { localStart, localEnd, isUpdating, updateTimeframe } = useDebouncedTimeframe({
    initialStart,
    initialEnd,
    onUpdate: mode === 'edit' && timeframe && onUpdate
      ? (start, end) => onUpdate(timeframe.id, { startTime: start, endTime: end })
      : () => {},
    debounceMs: 100
  })

  // Define validation before using it
  const isValidTimeframe = localEnd > localStart && localStart >= 0 && localEnd <= item.duration

  const handleSave = () => {
    if (mode === 'create' && onAdd) {
      onAdd(localStart, localEnd, loopCount)
    } else if (mode === 'edit' && timeframe && onUpdate) {
      onUpdate(timeframe.id, { loopCount })
    }
    onClose()
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts if user is typing in an input
      if (e.target instanceof HTMLInputElement) return

      if (e.key === 'Escape') {
        onClose()
      } else if (e.key === 'Enter' && isValidTimeframe) {
        handleSave()
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault()
        updateTimeframe(Math.max(0, localStart - 1), localEnd)
      } else if (e.key === 'ArrowRight') {
        e.preventDefault()
        updateTimeframe(Math.min(item.duration - 1, localStart + 1), localEnd)
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        updateTimeframe(localStart, Math.min(item.duration, localEnd + 1))
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        updateTimeframe(localStart, Math.max(localStart + 1, localEnd - 1))
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onClose, isValidTimeframe, localStart, localEnd, updateTimeframe, item.duration, handleSave])

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-dark-800 rounded-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto shadow-2xl border border-dark-600/50">
        {/* Header */}
        <div className="sticky top-0 bg-dark-800 border-b border-dark-600/50 p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center text-white font-bold text-lg">
                {mode === 'edit' ? '✏️' : '+'}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {mode === 'edit' ? 'Edit Timeframe' : 'Create New Timeframe'}
                </h2>
                <p className="text-dark-300">
                  {mode === 'edit' ? 'Adjust the time range and settings' : 'Select a time range from the video'}
                </p>
              </div>
            </div>

            {/* Action Buttons in Header */}
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="flex items-center justify-center w-10 h-10 bg-dark-600 hover:bg-dark-500 text-white rounded-lg transition-all hover:scale-105 active:scale-95"
                title="Cancel (Esc)"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
              <button
                onClick={handleSave}
                disabled={!isValidTimeframe}
                className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all ${
                  isValidTimeframe
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:scale-105 active:scale-95 shadow-lg'
                    : 'bg-dark-600 text-dark-400 cursor-not-allowed'
                }`}
                title={mode === 'edit' ? 'Save Changes (Enter)' : 'Create Timeframe (Enter)'}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-full">
          {/* Main Editing Area */}
          <div className="flex-1 p-4 space-y-4">
            {/* Video Info - Compact */}
            <div className="flex items-center gap-4 p-4 bg-dark-700/30 rounded-lg border border-dark-600/30">
              <img
                src={item.thumbnail}
                alt={item.title}
                className="w-20 h-15 object-cover rounded-lg shadow-lg"
              />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white line-clamp-2 mb-1">{item.title}</h3>
                <div className="flex items-center gap-3 text-sm text-dark-300">
                  <span>Duration: {formatTime(item.duration)}</span>
                  <span>•</span>
                  <span>{item.channel || 'YouTube'}</span>
                </div>
              </div>
            </div>

            {/* Main Editing Area */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Left Column - Timeframe Controls */}
            <div className="space-y-4">
              <div>
                <label className="text-lg font-semibold text-white block mb-3">
                  Select Time Range
                </label>

                {/* Compact Range Slider */}
                <div className="bg-dark-700/40 rounded-xl p-4">
                  <FocusedRangeSlider
                    min={0}
                    max={item.duration}
                    startValue={localStart}
                    endValue={localEnd}
                    onChange={updateTimeframe}
                    step={1}
                    className="mb-4"
                  />

                  {/* Compact Time Display */}
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-dark-600/50 rounded-lg p-3">
                      <div className="text-xs text-dark-400 mb-1">Start Time</div>
                      <div className="text-lg font-mono font-bold text-primary-300">
                        {formatTime(localStart)}
                      </div>
                    </div>
                    <div className="bg-dark-600/50 rounded-lg p-3">
                      <div className="text-xs text-dark-400 mb-1">Duration</div>
                      <div className="text-lg font-mono font-bold text-white">
                        {formatTime(localEnd - localStart)}
                      </div>
                    </div>
                    <div className="bg-dark-600/50 rounded-lg p-3">
                      <div className="text-xs text-dark-400 mb-1">End Time</div>
                      <div className="text-lg font-mono font-bold text-primary-300">
                        {formatTime(localEnd)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Loop Count */}
              <div className="bg-dark-700/40 rounded-xl p-4">
                <label className="text-base font-semibold text-white block mb-3">
                  Loop Count
                </label>
                <div className="flex items-center gap-3">
                  <input
                    type="number"
                    min="1"
                    max="99"
                    value={loopCount}
                    onChange={(e) => setLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
                    className="input-field text-base py-2 px-3 w-20 text-center font-mono"
                  />
                  <div className="text-sm text-dark-300">
                    times (total: {formatTime((localEnd - localStart) * loopCount)})
                  </div>
                </div>
              </div>

              {/* Status Indicator */}
              {isUpdating && (
                <div className="flex items-center justify-center gap-2 text-primary-400 bg-primary-500/10 rounded-lg p-3">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-400"></div>
                  <span className="text-sm">Updating timeframe...</span>
                </div>
              )}
            </div>

            {/* Right Column - Preview */}
            <div className="space-y-4">
              <label className="text-lg font-semibold text-white">
                Preview
              </label>

              <div className="bg-dark-700/40 rounded-xl p-4">
                <InlineTimeframePreview
                  videoId={item.id}
                  timeframe={{
                    id: timeframe?.id || 'preview',
                    startTime: localStart,
                    endTime: localEnd,
                    loopCount
                  }}
                  onClose={() => {}}
                />
              </div>
            </div>
            </div>

            {/* Keyboard Shortcuts Help */}
            <div className="pt-6 border-t border-dark-600/50">
              <div className="text-sm text-dark-400 space-y-1">
                <div>
                  <kbd className="bg-dark-600 px-2 py-1 rounded">Esc</kbd> to cancel
                  {isValidTimeframe && (
                    <>, <kbd className="bg-dark-600 px-2 py-1 rounded">Enter</kbd> to save</>
                  )}
                </div>
                <div className="text-xs">
                  <kbd className="bg-dark-600 px-1 rounded">←→</kbd> adjust start •
                  <kbd className="bg-dark-600 px-1 rounded">↑↓</kbd> adjust end
                </div>
              </div>
            </div>

            {/* Validation Error */}
            {!isValidTimeframe && (
              <div className="text-center">
                <div className="inline-block text-red-400 bg-red-500/10 px-6 py-3 rounded-xl border border-red-500/20">
                  End time must be greater than start time and within video duration
                </div>
              </div>
            )}
          </div>

          {/* Queue Context Sidebar */}
          {allItems.length > 1 && (
            <div className="w-80 border-l border-dark-600/50 p-6 bg-dark-800/50">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Queue Context</h3>
                <div className="text-xs text-dark-400 bg-dark-700/50 px-2 py-1 rounded">
                  {allItems.length} items
                </div>
              </div>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {allItems.map((queueItem, index) => (
                  <div
                    key={queueItem.draftId}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
                      queueItem.draftId === item.draftId
                        ? 'bg-primary-500/20 border border-primary-500/30'
                        : 'bg-dark-700/30 hover:bg-dark-700/50'
                    }`}
                  >
                    <div className="text-xs font-mono text-dark-400 w-6">#{index + 1}</div>
                    <img
                      src={queueItem.thumbnail}
                      alt={queueItem.title}
                      className="w-12 h-9 object-cover rounded"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-white line-clamp-1">{queueItem.title}</div>
                      <div className="text-xs text-dark-400">
                        {formatTime(queueItem.duration)} • {queueItem.timeframes.length} timeframes
                      </div>
                    </div>
                    {queueItem.draftId === item.draftId && (
                      <div className="text-xs text-primary-400 font-medium">Editing</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
