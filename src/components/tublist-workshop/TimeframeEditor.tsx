'use client'

import { useDebouncedTimeframe } from '@/hooks/useDebouncedTimeframe'
import { RangeSlider } from '@/components/ui/RangeSlider'
import { VideoTimeframe } from '@/lib/types/video'

interface TimeframeEditorProps {
  timeframe: VideoTimeframe
  maxDuration: number
  onUpdate: (timeframeId: string, updates: Partial<Pick<VideoTimeframe, 'startTime' | 'endTime' | 'loopCount'>>) => void
  onClose: () => void
}

export function TimeframeEditor({ timeframe, maxDuration, onUpdate, onClose }: TimeframeEditorProps) {
  const { localStart, localEnd, isUpdating, updateTimeframe } = useDebouncedTimeframe({
    initialStart: timeframe.startTime,
    initialEnd: timeframe.endTime,
    onUpdate: (start, end) => {
      onUpdate(timeframe.id, { startTime: start, endTime: end })
    },
    debounceMs: 150
  })

  return (
    <div className="mt-4 p-5 bg-dark-700/60 rounded-xl border border-dark-600/50 shadow-lg">
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center gap-3">
          <label className="text-base text-dark-100 font-semibold">Edit Time Range</label>
          {isUpdating && (
            <div className="flex items-center gap-2 text-sm text-primary-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-400"></div>
              Updating...
            </div>
          )}
        </div>

        {/* Action Button in Header */}
        <button
          onClick={onClose}
          className="flex items-center justify-center w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all hover:scale-105 active:scale-95 shadow-lg"
          title="Done Editing"
        >
          <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
        </button>
      </div>

      <RangeSlider
        min={0}
        max={maxDuration}
        startValue={localStart}
        endValue={localEnd}
        onChange={updateTimeframe}
        step={1}
        className="mb-5"
      />


    </div>
  )
}
