'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { MagicPromptInput } from './MagicPromptInput'
import { MagicQueueResults } from './MagicQueueResults'
import { aiService, MagicQueueResponse } from '@/lib/services/ai'
import { useDraftQueue } from '@/hooks/useTublistWorkshop'
import { useI18n } from '@/hooks/useI18n'
import { useNavigation } from '@/hooks/useNavigation'
import { formatDuration } from '@/lib/utils/format'
import { DraftQueue } from '@/components/tublist-workshop/DraftQueue'
import { QueueCreationForm, QueueCreationFormRef } from '@/components/tublist-workshop/QueueCreationForm'
import { useAuth } from '@/hooks/useAuth'

export function MagicQueueView() {
  const { user, isAuthenticated } = useAuth()
  const { setActiveView } = useNavigation()
  const {
    draftItems,
    draftCount,
    draftDuration,
    isCreationMode,
    isEditMode,
    editingQueueId,
    enterCreationMode,
    exitCreationMode,
    exitEditMode,
    saveDraftAsQueue,
    updateExistingQueue
  } = useDraftQueue()

  // Magic queue state
  const [isGenerating, setIsGenerating] = useState(false)
  const [magicResults, setMagicResults] = useState<MagicQueueResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [progressStep, setProgressStep] = useState<string>('')
  const [progressCurrent, setProgressCurrent] = useState<number>(0)
  const [progressTotal, setProgressTotal] = useState<number>(0)
  const { t } = useI18n()

  // Queue creation form ref and state
  const queueFormRef = useRef<QueueCreationFormRef>(null)
  const [formCanSave, setFormCanSave] = useState(false)
  const [formIsSaving, setFormIsSaving] = useState(false)

  // Automatically enter creation mode when MagicQueueView becomes active (unless already in edit mode)
  useEffect(() => {
    if (!isCreationMode && !isEditMode) {
      enterCreationMode()
    }
  }, [isCreationMode, isEditMode, enterCreationMode])

  // Queue creation handlers
  const handleSaveQueue = () => {
    queueFormRef.current?.save()
  }

  const handleCancelQueue = () => {
    queueFormRef.current?.cancel()
  }

  // Handle form state changes
  const handleFormStateChange = useCallback((canSave: boolean, isSaving: boolean) => {
    setFormCanSave(canSave)
    setFormIsSaving(isSaving)
  }, [])

  const handleGenerateMagicQueue = async (prompt: string, count: number, maxDuration?: number) => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    setError(null)
    setMagicResults(null)
    setProgressStep('Starting...')
    setProgressCurrent(0)
    setProgressTotal(3)

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      console.log('📊 Parameters:', { count, maxDuration })

      const response = await aiService.generateVideoRecommendations({
        prompt: prompt.trim(),
        count,
        maxDuration,
        onProgress: (step: string, current: number, total: number) => {
          setProgressStep(step)
          setProgressCurrent(current)
          setProgressTotal(total)
        }
      })

      setMagicResults(response)
      console.log('✅ Magic queue generated successfully:', response)
    } catch (error) {
      console.error('❌ Failed to generate magic queue:', error)
      setError(error instanceof Error ? error.message : 'Failed to generate magic queue. Please try again.')
    } finally {
      setIsGenerating(false)
      setProgressStep('')
      setProgressCurrent(0)
      setProgressTotal(0)
    }
  }



  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header - Sticky */}
      {(isCreationMode || isEditMode) && (
        <div className="sticky top-16 z-[60] glassmorphism-strong p-4 mb-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {/* Main Icon */}
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                {isEditMode ? (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                  </svg>
                ) : (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6ZM19.5 15.4L22 14L20.6 16.5L22 19L19.5 17.6L17 19L18.4 16.5L17 14L19.5 15.4ZM22 2L20.6 4.5L22 7L19.5 5.6L17 7L18.4 4.5L17 2L19.5 3.4L22 2Z"/>
                  </svg>
                )}
              </div>

              {/* Video Count Icon */}
              <div className="flex items-center space-x-1 text-white">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-primary-400">
                  <path d="M8 5v14l11-7z"/>
                </svg>
                <span className="text-sm font-medium">{draftCount}</span>
              </div>

              {/* Duration Icon */}
              <div className="flex items-center space-x-1 text-dark-300">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span className="text-xs">{formatDuration(draftDuration)}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={handleCancelQueue}
                className="p-2 text-dark-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                title={t('common.cancel')}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
              <button
                onClick={handleSaveQueue}
                disabled={!formCanSave}
                className="p-2 bg-primary-500 hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200"
                title={isEditMode ? t('buttons.updateQueue') : t('buttons.createQueue')}
              >
                {formIsSaving ? (
                  <div className="loading-spinner w-4 h-4"></div>
                ) : (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Magic Prompt Input */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            {isEditMode ? t('magicQueue.editQueueRecommendations') : t('magicQueue.generator')}
          </h1>
          <p className="text-dark-300">
            {isEditMode
              ? t('magicQueue.editDescription')
              : t('magicQueue.description')
            }
          </p>
        </div>

        <MagicPromptInput
          onSubmit={handleGenerateMagicQueue}
          isLoading={isGenerating}
          placeholder={t('magic.placeholder')}
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="glassmorphism rounded-2xl p-6 border border-red-500/20">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 text-red-400 flex-shrink-0">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h3 className="text-red-400 font-semibold mb-1">{t('common.error')}</h3>
              <p className="text-dark-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Magic Queue Results */}
      {(magicResults || isGenerating) && (
        <MagicQueueResults
          videos={magicResults?.videos || []}
          explanation={magicResults?.explanation}
          searchQueries={magicResults?.searchQueries}
          isLoading={isGenerating}
          progressStep={progressStep}
          progressCurrent={progressCurrent}
          progressTotal={progressTotal}
        />
      )}

      {/* Draft Queue Display (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && <DraftQueue />}

      {/* Queue Creation/Edit Form (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && (
        <QueueCreationForm
          ref={queueFormRef}
          onFormStateChange={handleFormStateChange}
        />
      )}
    </div>
  )
}
