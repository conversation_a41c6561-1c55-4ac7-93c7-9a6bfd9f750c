'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { formatTime } from '@/lib/utils/time'
import { debounce } from '@/lib/utils/format'

interface FocusedRangeSliderProps {
  min: number
  max: number
  startValue: number
  endValue: number
  onChange: (start: number, end: number) => void
  step?: number
  className?: string
  disabled?: boolean
}

export function FocusedRangeSlider({
  min,
  max,
  startValue,
  endValue,
  onChange,
  step = 1,
  className = '',
  disabled = false
}: FocusedRangeSliderProps) {
  const [isDragging, setIsDragging] = useState<'start' | 'end' | null>(null)
  const [localStart, setLocalStart] = useState(startValue)
  const [localEnd, setLocalEnd] = useState(endValue)
  const sliderRef = useRef<HTMLDivElement>(null)

  // Update local state when props change
  useEffect(() => {
    setLocalStart(startValue)
    setLocalEnd(endValue)
  }, [startValue, endValue])

  // Ensure values are within bounds
  const clampedStart = Math.max(min, Math.min(max - step, localStart))
  const clampedEnd = Math.max(clampedStart + step, Math.min(max, localEnd))

  // Debounced onChange to reduce performance impact on API calls
  const debouncedOnChange = useCallback(
    debounce((start: number, end: number) => {
      onChange(start, end)
    }, 100), // Keep some debounce for API calls
    [onChange]
  )

  // Update local state immediately for responsive UI, trigger debounced onChange for API
  const updateValues = useCallback((newStart: number, newEnd: number) => {
    setLocalStart(newStart)
    setLocalEnd(newEnd)
    // Immediate callback for responsive UI
    onChange(newStart, newEnd)
  }, [onChange])

  const getValueFromPosition = useCallback((clientX: number) => {
    if (!sliderRef.current) return min

    const rect = sliderRef.current.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
    const value = min + percentage * (max - min)
    
    // Round to nearest step
    return Math.round(value / step) * step
  }, [min, max, step])

  const handlePointerDown = useCallback((handle: 'start' | 'end') => (e: React.PointerEvent) => {
    if (disabled) return
    e.preventDefault()
    setIsDragging(handle)
    e.currentTarget.setPointerCapture(e.pointerId)
  }, [disabled])

  const handlePointerMove = useCallback((e: PointerEvent) => {
    if (!isDragging || disabled) return

    const newValue = getValueFromPosition(e.clientX)

    if (isDragging === 'start') {
      const newStart = Math.min(newValue, clampedEnd - step)
      setLocalStart(newStart)
      // Immediate update for responsive UI
      onChange(newStart, clampedEnd)
    } else {
      const newEnd = Math.max(newValue, clampedStart + step)
      setLocalEnd(newEnd)
      // Immediate update for responsive UI
      onChange(clampedStart, newEnd)
    }
  }, [isDragging, disabled, getValueFromPosition, clampedStart, clampedEnd, step, onChange])

  const handlePointerUp = useCallback(() => {
    setIsDragging(null)
  }, [])

  // Handle pointer events
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('pointermove', handlePointerMove)
      document.addEventListener('pointerup', handlePointerUp)
      return () => {
        document.removeEventListener('pointermove', handlePointerMove)
        document.removeEventListener('pointerup', handlePointerUp)
      }
    }
  }, [isDragging, handlePointerMove, handlePointerUp])

  // Calculate positions as percentages
  const startPercentage = ((clampedStart - min) / (max - min)) * 100
  const endPercentage = ((clampedEnd - min) / (max - min)) * 100

  const adjustStartTime = useCallback((delta: number) => {
    if (disabled) return
    const newStart = Math.max(min, Math.min(clampedEnd - step, clampedStart + delta))
    updateValues(newStart, clampedEnd)
  }, [disabled, min, clampedEnd, step, clampedStart, updateValues])

  const adjustEndTime = useCallback((delta: number) => {
    if (disabled) return
    const newEnd = Math.max(clampedStart + step, Math.min(max, clampedEnd + delta))
    updateValues(clampedStart, newEnd)
  }, [disabled, max, clampedStart, step, clampedEnd, updateValues])

  return (
    <div className={`relative ${className}`}>
      {/* Time labels */}
      <div className="flex justify-between text-sm text-dark-400 mb-6">
        <span className="font-mono">{formatTime(min)}</span>
        <span className="font-mono">{formatTime(max)}</span>
      </div>
      
      {/* Active dragging indicator */}
      {isDragging && (
        <div className="absolute -top-8 left-0 right-0 text-center">
          <div className="inline-block bg-primary-500 text-white text-sm px-4 py-2 rounded-full shadow-lg">
            Adjusting {isDragging} time: {formatTime(isDragging === 'start' ? clampedStart : clampedEnd)}
          </div>
        </div>
      )}

      {/* Compact Slider track */}
      <div
        ref={sliderRef}
        className={`relative h-12 bg-dark-700 rounded-2xl cursor-pointer transition-all hover:bg-dark-600 shadow-inner ${
          disabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        onPointerDown={(e) => {
          if (disabled) return
          const newValue = getValueFromPosition(e.clientX)
          const startDistance = Math.abs(newValue - clampedStart)
          const endDistance = Math.abs(newValue - clampedEnd)

          if (startDistance < endDistance) {
            const newStart = Math.min(newValue, clampedEnd - step)
            updateValues(newStart, clampedEnd)
          } else {
            const newEnd = Math.max(newValue, clampedStart + step)
            updateValues(clampedStart, newEnd)
          }
        }}
      >
        {/* Selected range */}
        <div
          className="absolute top-0 h-full bg-primary-500/60 rounded-2xl transition-all shadow-lg"
          style={{
            left: `${startPercentage}%`,
            width: `${endPercentage - startPercentage}%`
          }}
        />

        {/* Start handle - Compact */}
        <div
          className={`absolute top-1/2 w-10 h-10 bg-primary-500 border-2 border-white rounded-full transform -translate-y-1/2 cursor-grab shadow-xl transition-all touch-none ${
            isDragging === 'start' ? 'scale-125 cursor-grabbing shadow-2xl ring-4 ring-primary-500/30' : 'hover:scale-110 hover:shadow-2xl'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ left: `calc(${startPercentage}% - 20px)` }}
          onPointerDown={handlePointerDown('start')}
        />

        {/* End handle - Compact */}
        <div
          className={`absolute top-1/2 w-10 h-10 bg-primary-500 border-2 border-white rounded-full transform -translate-y-1/2 cursor-grab shadow-xl transition-all touch-none ${
            isDragging === 'end' ? 'scale-125 cursor-grabbing shadow-2xl ring-4 ring-primary-500/30' : 'hover:scale-110 hover:shadow-2xl'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ left: `calc(${endPercentage}% - 20px)` }}
          onPointerDown={handlePointerDown('end')}
        />
      </div>

      {/* Compact precision controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        {/* Start Time Controls */}
        <div className="text-center">
          <div className="text-primary-300 text-xl mb-3">
            <div className="text-dark-400 text-base mb-1">Start Time</div>
            <div className="font-mono font-bold">{formatTime(clampedStart)}</div>
          </div>
          <div className="flex items-center justify-center gap-3">
            <button
              onClick={() => adjustStartTime(-step)}
              disabled={disabled || clampedStart <= min}
              className={`w-12 h-12 rounded-xl flex items-center justify-center text-xl font-bold transition-all ${
                disabled || clampedStart <= min
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-110 active:scale-95 shadow-lg'
              }`}
              title="Decrease start time by 1 second"
            >
              −
            </button>
            <button
              onClick={() => adjustStartTime(step)}
              disabled={disabled || clampedStart >= clampedEnd - step}
              className={`w-12 h-12 rounded-xl flex items-center justify-center text-xl font-bold transition-all ${
                disabled || clampedStart >= clampedEnd - step
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-110 active:scale-95 shadow-lg'
              }`}
              title="Increase start time by 1 second"
            >
              +
            </button>
          </div>
        </div>

        {/* Duration Display */}
        <div className="text-center">
          <div className="text-dark-400 bg-dark-700/50 px-6 py-4 rounded-xl">
            <div className="text-base mb-1 text-dark-300">Selected Duration</div>
            <div className="text-2xl font-mono font-bold text-primary-300 mb-1">
              {formatTime(clampedEnd - clampedStart)}
            </div>
            <div className="text-xs text-dark-400">
              {((clampedEnd - clampedStart) / (max - min) * 100).toFixed(1)}% of video
            </div>
          </div>
        </div>

        {/* End Time Controls */}
        <div className="text-center">
          <div className="text-primary-300 text-xl mb-3">
            <div className="text-dark-400 text-base mb-1">End Time</div>
            <div className="font-mono font-bold">{formatTime(clampedEnd)}</div>
          </div>
          <div className="flex items-center justify-center gap-3">
            <button
              onClick={() => adjustEndTime(-step)}
              disabled={disabled || clampedEnd <= clampedStart + step}
              className={`w-12 h-12 rounded-xl flex items-center justify-center text-xl font-bold transition-all ${
                disabled || clampedEnd <= clampedStart + step
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-110 active:scale-95 shadow-lg'
              }`}
              title="Decrease end time by 1 second"
            >
              −
            </button>
            <button
              onClick={() => adjustEndTime(step)}
              disabled={disabled || clampedEnd >= max}
              className={`w-12 h-12 rounded-xl flex items-center justify-center text-xl font-bold transition-all ${
                disabled || clampedEnd >= max
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-110 active:scale-95 shadow-lg'
              }`}
              title="Increase end time by 1 second"
            >
              +
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
