#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get the project root directory (two levels up from this script)
const projectRoot = path.resolve(__dirname, '../../');
const firebaseConfigDir = path.resolve(__dirname, '../config/');

// Parse command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'development';
const deployType = args[1] || 'all'; // all, hosting, firestore, rules, indexes

// Validate environment
const validEnvironments = ['development', 'production'];
if (!validEnvironments.includes(environment)) {
  console.error(`❌ Invalid environment: ${environment}`);
  console.error(`Valid environments: ${validEnvironments.join(', ')}`);
  process.exit(1);
}

// Validate deploy type
const validDeployTypes = ['all', 'hosting', 'firestore', 'rules', 'indexes'];
if (!validDeployTypes.includes(deployType)) {
  console.error(`❌ Invalid deploy type: ${deployType}`);
  console.error(`Valid deploy types: ${validDeployTypes.join(', ')}`);
  process.exit(1);
}

console.log(`🚀 Deploying to ${environment} environment...`);
console.log(`📦 Deploy type: ${deployType}`);

try {
  // Build the Next.js app if deploying hosting or all
  if (deployType === 'hosting' || deployType === 'all') {
    console.log(`🔨 Building Next.js app for ${environment}...`);

    // Always use production build for static export, but set app environment
    const buildCommand = environment === 'production'
      ? 'NODE_ENV=production npm run build'
      : 'NODE_ENV=production NEXT_PUBLIC_APP_ENV=development npm run build';

    console.log(`🚀 Running: ${buildCommand}`);
    execSync(buildCommand, { stdio: 'inherit', cwd: projectRoot });

    // Verify build output exists
    const buildOutputDir = path.join(projectRoot, 'out');
    if (!fs.existsSync(buildOutputDir)) {
      console.error(`❌ Build output directory not found: ${buildOutputDir}`);
      console.error(`Make sure Next.js build completed successfully`);
      process.exit(1);
    }

    // Copy build output to firebase config directory to avoid path issues
    const firebaseOutDir = path.join(firebaseConfigDir, 'out');
    console.log(`📁 Copying build output to Firebase config directory...`);

    // Remove existing out directory in firebase config if it exists
    if (fs.existsSync(firebaseOutDir)) {
      execSync(`rm -rf "${firebaseOutDir}"`, { cwd: firebaseConfigDir });
    }

    // Copy the out directory
    execSync(`cp -r "${buildOutputDir}" "${firebaseOutDir}"`, { cwd: projectRoot });

    console.log(`✅ Next.js build completed successfully`);
  }
  // Use the single firebase.json config file
  const configFile = path.join(firebaseConfigDir, 'firebase.json');

  if (!fs.existsSync(configFile)) {
    console.error(`❌ Firebase config not found: ${configFile}`);
    process.exit(1);
  }

  console.log(`📋 Using Firebase config: firebase.json`);
  console.log(`🎯 Target environment: ${environment}`);
  console.log(`🔥 Deploy type: ${deployType}`);

  // Construct Firebase deploy command
  let deployCommand = 'firebase deploy';

  // Add project flag
  deployCommand += ` --project ${environment}`;

  // Add deploy type flag
  if (deployType === 'all') {
    // Deploy everything (hosting + firestore)
    deployCommand += ' --only hosting,firestore';
  } else if (deployType === 'hosting') {
    deployCommand += ' --only hosting';
  } else if (deployType === 'firestore') {
    deployCommand += ' --only firestore';
  } else if (deployType === 'rules') {
    deployCommand += ' --only firestore:rules';
  } else if (deployType === 'indexes') {
    deployCommand += ' --only firestore:indexes';
  }

  console.log(`🚀 Running: ${deployCommand}`);
  execSync(deployCommand, { stdio: 'inherit', cwd: firebaseConfigDir });

  // Clean up copied build output if it was created for hosting deployment
  if (deployType === 'hosting' || deployType === 'all') {
    const firebaseOutDir = path.join(firebaseConfigDir, 'out');
    if (fs.existsSync(firebaseOutDir)) {
      console.log(`🧹 Cleaning up temporary build files...`);
      execSync(`rm -rf "${firebaseOutDir}"`, { cwd: firebaseConfigDir });
    }
  }

  console.log(`✅ Successfully deployed to ${environment}!`);

  // Show helpful information
  if (environment === 'production') {
    console.log(`🎯 Deployed to production Firebase project`);
    if (deployType === 'hosting' || deployType === 'all') {
      console.log(`🌐 Your app is live at: https://looper-2630d.web.app`);
    }
  } else {
    console.log(`🎯 Deployed to development Firebase project`);
    if (deployType === 'hosting' || deployType === 'all') {
      console.log(`🌐 Your app is live at: https://tubli-dev.web.app`);
    }
  }

} catch (error) {
  // Clean up copied build output if it exists
  const firebaseOutDir = path.join(firebaseConfigDir, 'out');
  if (fs.existsSync(firebaseOutDir)) {
    console.log(`🧹 Cleaning up temporary build files...`);
    try {
      execSync(`rm -rf "${firebaseOutDir}"`, { cwd: firebaseConfigDir });
    } catch (cleanupError) {
      console.warn(`⚠️ Failed to clean up temporary files: ${cleanupError.message}`);
    }
  }

  console.error(`❌ Deployment failed:`, error.message);
  process.exit(1);
}
