{"hosting": {"public": "../../out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}}}